# 时间轴拖拽自动滚动功能实现

## 功能描述

当用户拖拽时间轴中的出点和入点指示器时，如果鼠标指针接近时间轴容器的顶部或底部边缘，会自动触发滚动，让用户可以继续拖拽到视口之外的区域。

## 实现细节

### 1. 自动滚动函数 (`handleAutoScroll`)

```typescript
const handleAutoScroll = useCallback((clientY: number) => {
  const container = document.querySelector('.timeline-main-container') as HTMLElement;
  if (!container) return;

  const containerRect = container.getBoundingClientRect();
  const scrollThreshold = 50; // 距离边缘多少像素开始滚动
  const scrollSpeed = 5; // 滚动速度

  // 计算鼠标相对于容器的位置
  const relativeY = clientY - containerRect.top;
  const containerHeight = containerRect.height;

  let shouldScroll = false;
  let scrollDirection = 0;

  // 检查是否接近顶部
  if (relativeY < scrollThreshold && container.scrollTop > 0) {
    shouldScroll = true;
    scrollDirection = -scrollSpeed;
  }
  // 检查是否接近底部
  else if (relativeY > containerHeight - scrollThreshold) {
    const maxScrollTop = container.scrollHeight - container.clientHeight;
    if (container.scrollTop < maxScrollTop) {
      shouldScroll = true;
      scrollDirection = scrollSpeed;
    }
  }

  if (shouldScroll) {
    // 使用 requestAnimationFrame 实现平滑滚动
    const scroll = () => {
      container.scrollTop += scrollDirection;
      autoScrollRef.current = requestAnimationFrame(scroll);
    };
    autoScrollRef.current = requestAnimationFrame(scroll);
  } else {
    // 停止自动滚动
    if (autoScrollRef.current) {
      cancelAnimationFrame(autoScrollRef.current);
      autoScrollRef.current = null;
    }
  }
}, []);
```

### 2. 停止自动滚动函数 (`stopAutoScroll`)

```typescript
const stopAutoScroll = useCallback(() => {
  if (autoScrollRef.current) {
    cancelAnimationFrame(autoScrollRef.current);
    autoScrollRef.current = null;
  }
}, []);
```

### 3. 集成到拖拽事件处理中

在以下三个拖拽处理函数的 `handleMouseMoveGlobal` 中添加了自动滚动调用：

- `handleInPointDragStart` - 入点拖拽
- `handleOutPointDragStart` - 出点拖拽  
- `handlePlayheadDragStart` - 播放头拖拽

在每个 `handleMouseUpGlobal` 中添加了停止自动滚动的调用。

### 4. 配置参数

- **scrollThreshold**: 50px - 距离容器边缘多少像素开始触发自动滚动
- **scrollSpeed**: 5px - 每帧滚动的像素数
- 使用 `requestAnimationFrame` 确保平滑的滚动体验

### 5. 生命周期管理

- 添加了 `useEffect` 清理函数，确保组件卸载时停止自动滚动
- 使用 `useRef` 存储动画帧ID，避免内存泄漏

## 使用体验

1. 用户开始拖拽入点、出点或播放头指示器
2. 当鼠标接近时间轴容器顶部或底部50px范围内时，自动开始滚动
3. 滚动方向根据鼠标位置自动判断（顶部向上滚动，底部向下滚动）
4. 拖拽结束时自动停止滚动
5. 滚动速度适中，提供良好的用户体验

## 技术要点

- 使用 `requestAnimationFrame` 实现平滑滚动
- 通过 `getBoundingClientRect()` 精确计算鼠标相对位置
- 边界检查防止过度滚动
- 内存管理防止动画帧泄漏
- 与现有拖拽逻辑无缝集成

这个实现让用户在拖拽时间轴指示器时能够自然地滚动到视口之外的区域，大大提升了长时间轴的操作体验。
