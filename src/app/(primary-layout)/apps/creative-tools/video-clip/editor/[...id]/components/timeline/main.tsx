"use client";

import React, {
  useEffect,
  useState,
  useRef,
  Fragment,
  useMemo,
  useCallback,
} from "react";
import { Spin } from "antd";
import { useTimelineStore } from "../../stores/timeline";
import { useEditorStore } from "../../stores/editor";
import { VideoClipData } from "../../types";
import { getShortDramaFrame } from "../../services";
import { Ruler } from "./ruler";
import { SubtitleTrack } from "./subtitle-track";

const TIMELINE_CONSTANTS = {
  PIXELS_PER_SECOND: 50,
  FRAME_HEIGHT: 50, // 预览帧高度
  FRAME_WIDTH: 40, // 预览帧宽度
  FRAME_SAMPLE_RATE: 1, // 帧采样率（每秒几帧）
  VIDEO_TRACK_WIDTH: 40, // 视频轨道宽度
};

/**
 * 竖向时间刻度尺组件
 * 显示时间标记和刻度线，支持智能缩放和格式化
 * 使用公共状态管理时间轴数据，并显示视频轨道和帧预览
 */
export function MainContainer() {
  const {
    duration,
    zoomLevel,
    currentTime,
    seekToTime,
    setDuration,
    setPreviewTime,
  } = useTimelineStore();
  const {
    videoClips,
    selectedClips,
    activeClip,
    setActiveClip,
    setVideoClips,
  } = useEditorStore();

  // 帧预览相关状态
  const [videoFrames, setVideoFrames] = useState<Record<string, string[]>>({});
  const [loadingFrames, setLoadingFrames] = useState<Record<string, boolean>>(
    {}
  );
  // 拖拽相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragType, setDragType] = useState<
    "inPoint" | "outPoint" | "playhead" | null
  >(null);
  const [draggingClipId, setDraggingClipId] = useState<string | null>(null);
  const dragStartRef = useRef<{ y: number; originalValue: number } | null>(
    null
  );
  const finalDragValueRef = useRef<number | null>(null);

  // 实时拖拽值状态 - 用于立即更新UI显示
  const [liveInPoint, setLiveInPoint] = useState<Record<string, number>>({});
  const [liveOutPoint, setLiveOutPoint] = useState<Record<string, number>>({});
  const [liveCurrentTime, setLiveCurrentTime] = useState<number | null>(null);

  // 自动滚动相关状态
  const autoScrollRef = useRef<number | null>(null);
  const currentMouseYRef = useRef<number>(0); // 当前鼠标Y位置
  const dragStartScrollTopRef = useRef<number>(0); // 拖拽开始时的滚动位置

  /**
   * 自动滚动函数
   * 当鼠标接近容器边缘时触发持续滚动
   */
  const handleAutoScroll = useCallback((clientY: number) => {
    // 更新当前鼠标位置
    currentMouseYRef.current = clientY;

    const container = document.querySelector(
      ".timeline-main-container"
    ) as HTMLElement;
    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const scrollThreshold = 50; // 距离边缘多少像素开始滚动
    const scrollSpeed = 4; // 滚动速度

    // 计算鼠标相对于容器的位置
    const relativeY = clientY - containerRect.top;
    const containerHeight = containerRect.height;

    let shouldScroll = false;
    let scrollDirection = 0;

    // 检查是否接近顶部
    if (relativeY < scrollThreshold && container.scrollTop > 0) {
      shouldScroll = true;
      scrollDirection = -scrollSpeed;
    }
    // 检查是否接近底部
    else if (relativeY > containerHeight - scrollThreshold) {
      const maxScrollTop = container.scrollHeight - container.clientHeight;
      if (container.scrollTop < maxScrollTop) {
        shouldScroll = true;
        scrollDirection = scrollSpeed;
      }
    }

    if (shouldScroll) {
      // 立即执行一次滚动
      container.scrollTop += scrollDirection;

      // 如果没有正在进行的自动滚动，开始新的滚动动画
      if (!autoScrollRef.current) {
        const scroll = () => {
          const currentContainer = document.querySelector(
            ".timeline-main-container"
          ) as HTMLElement;
          if (!currentContainer) {
            autoScrollRef.current = null;
            return;
          }

          // 使用最新的鼠标位置
          const currentClientY = currentMouseYRef.current;
          const currentContainerRect = currentContainer.getBoundingClientRect();
          const currentRelativeY = currentClientY - currentContainerRect.top;
          const currentContainerHeight = currentContainerRect.height;

          let currentShouldScroll = false;
          let currentScrollDirection = 0;

          // 检查是否接近顶部
          if (
            currentRelativeY < scrollThreshold &&
            currentContainer.scrollTop > 0
          ) {
            currentShouldScroll = true;
            currentScrollDirection = -scrollSpeed;
          }
          // 检查是否接近底部
          else if (
            currentRelativeY >
            currentContainerHeight - scrollThreshold
          ) {
            const currentMaxScrollTop =
              currentContainer.scrollHeight - currentContainer.clientHeight;
            if (currentContainer.scrollTop < currentMaxScrollTop) {
              currentShouldScroll = true;
              currentScrollDirection = scrollSpeed;
            }
          }

          if (currentShouldScroll) {
            // 执行滚动
            currentContainer.scrollTop += currentScrollDirection;

            // 继续下一帧滚动
            autoScrollRef.current = requestAnimationFrame(scroll);
          } else {
            // 停止滚动
            autoScrollRef.current = null;
          }
        };

        autoScrollRef.current = requestAnimationFrame(scroll);
      }
    } else {
      // 停止自动滚动
      if (autoScrollRef.current) {
        cancelAnimationFrame(autoScrollRef.current);
        autoScrollRef.current = null;
      }
    }
  }, []);

  /**
   * 获取当前累积的滚动时间偏移量
   */
  const getScrollTimeOffset = useCallback(() => {
    const container = document.querySelector(
      ".timeline-main-container"
    ) as HTMLElement;
    if (!container) return 0;

    // 计算从拖拽开始到现在的滚动偏移量
    const scrollOffset = container.scrollTop - dragStartScrollTopRef.current;
    return scrollOffset / (TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel);
  }, [zoomLevel]);

  /**
   * 停止自动滚动
   */
  const stopAutoScroll = useCallback(() => {
    if (autoScrollRef.current) {
      cancelAnimationFrame(autoScrollRef.current);
      autoScrollRef.current = null;
    }
  }, []);

  // 组件卸载时清理自动滚动
  useEffect(() => {
    return () => {
      stopAutoScroll();
    };
  }, [stopAutoScroll]);

  // 计算视频片段的总时长
  useEffect(() => {
    if (videoClips && videoClips.length > 0) {
      // 找到最大的 endTime 作为总时长
      const totalDuration = Math.max(
        ...videoClips.map((clip: VideoClipData) => clip.endTime)
      );
      setDuration(totalDuration);
    } else {
      // 如果没有视频片段，使用默认时长
      setDuration(60);
    }
  }, [videoClips, setDuration]);

  /**
   * 检查片段是否为激活状态
   */
  const isActiveClip = (clipId: string): boolean => {
    return activeClip && String(activeClip.id) === clipId;
  };

  /**
   * 获取实时的入点出点值（优先使用拖拽中的值）
   */
  const getLiveClipPoints = (clip: VideoClipData) => {
    const currentInPoint =
      isDragging && draggingClipId === clip.id && dragType === "inPoint"
        ? liveInPoint[clip.id] ?? clip.inPoint
        : clip.inPoint;

    const currentOutPoint =
      isDragging && draggingClipId === clip.id && dragType === "outPoint"
        ? liveOutPoint[clip.id] ?? clip.outPoint
        : clip.outPoint;

    return { currentInPoint, currentOutPoint };
  };

  /**
   * 处理片段激活
   */
  const handleClipActivate = (clip: VideoClipData) => {
    const originalClip = selectedClips.find(
      (sc: any) => String(sc.id) === clip.id
    );
    if (originalClip && (!activeClip || String(activeClip.id) !== clip.id)) {
      setActiveClip(originalClip);
    }
  };

  /**
   * 处理入点拖拽开始
   */
  const handleInPointDragStart = useCallback(
    (e: React.MouseEvent, clip: VideoClipData) => {
      e.preventDefault();
      e.stopPropagation();

      // 如果片段未激活，先激活片段
      if (!isActiveClip(clip.id)) {
        handleClipActivate(clip);
        // 激活后继续执行拖拽逻辑，不要返回
      }

      setIsDragging(true);
      setDragType("inPoint");
      setDraggingClipId(clip.id);

      // 记录拖拽开始时的滚动位置
      const container = document.querySelector(
        ".timeline-main-container"
      ) as HTMLElement;
      dragStartScrollTopRef.current = container ? container.scrollTop : 0;

      dragStartRef.current = {
        y: e.clientY,
        originalValue: clip.inPoint,
      };

      // 创建新的事件处理函数，避免闭包问题
      const handleMouseMoveGlobal = (e: MouseEvent) => {
        if (!dragStartRef.current || !clip.id) return;

        const container = document.querySelector(
          ".timeline-main-container"
        ) as HTMLElement;
        if (!container) return;

        // 先计算当前的滚动偏移量
        const currentScrollOffset =
          container.scrollTop - dragStartScrollTopRef.current;

        // 预测即将发生的滚动
        const containerRect = container.getBoundingClientRect();
        const relativeY = e.clientY - containerRect.top;
        const containerHeight = containerRect.height;
        const scrollThreshold = 50;

        let predictedScrollOffset = currentScrollOffset;

        // 如果鼠标在滚动区域，预测下一帧的滚动偏移
        if (relativeY < scrollThreshold && container.scrollTop > 0) {
          predictedScrollOffset += 4; // 预测向上滚动
        } else if (relativeY > containerHeight - scrollThreshold) {
          const maxScrollTop = container.scrollHeight - container.clientHeight;
          if (container.scrollTop < maxScrollTop) {
            predictedScrollOffset -= 4; // 预测向下滚动
          }
        }

        // 处理自动滚动
        handleAutoScroll(e.clientY);

        const deltaY = e.clientY - dragStartRef.current.y;
        const sensitivity = e.shiftKey ? 0.1 : 1;
        const deltaTime =
          (deltaY * sensitivity) /
          (TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel);

        // 使用预测的滚动偏移量
        const scrollTimeOffset =
          predictedScrollOffset /
          (TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel);

        const newValue = Math.max(
          0,
          dragStartRef.current.originalValue + deltaTime + scrollTimeOffset
        );

        // 限制范围
        const constrainedValue = Math.max(
          0,
          Math.min(clip.outPoint - 0.1, newValue)
        );

        // 保存最终值到 ref
        finalDragValueRef.current = constrainedValue;

        // 更新实时显示值
        setLiveInPoint((prev) => ({
          ...prev,
          [clip.id]: constrainedValue,
        }));

        // 设置预览时间为入点的全局时间位置
        const previewGlobalTime = clip.startTime + constrainedValue;
        setPreviewTime(previewGlobalTime);
      };

      const handleMouseUpGlobal = () => {
        // 停止自动滚动
        stopAutoScroll();

        // 获取最终的拖拽值
        const finalValue = finalDragValueRef.current ?? clip.inPoint;

        // 更新全局状态
        const updatedClips = videoClips.map((c: VideoClipData) =>
          c.id === clip.id ? { ...c, inPoint: finalValue } : c
        );
        setVideoClips(updatedClips);

        // 清理实时状态
        setLiveInPoint((prev) => {
          const newState = { ...prev };
          delete newState[clip.id];
          return newState;
        });

        // 清理预览时间
        setPreviewTime(null);

        // 清理 ref
        finalDragValueRef.current = null;

        setIsDragging(false);
        setDragType(null);
        setDraggingClipId(null);
        dragStartRef.current = null;

        document.removeEventListener("mousemove", handleMouseMoveGlobal);
        document.removeEventListener("mouseup", handleMouseUpGlobal);
      };

      document.addEventListener("mousemove", handleMouseMoveGlobal);
      document.addEventListener("mouseup", handleMouseUpGlobal);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      isActiveClip,
      handleClipActivate,
      setLiveInPoint,
      setIsDragging,
      setDragType,
      setDraggingClipId,
      zoomLevel,
      handleAutoScroll,
      stopAutoScroll,
    ]
  );

  /**
   * 处理出点拖拽开始
   */
  const handleOutPointDragStart = useCallback(
    (e: React.MouseEvent, clip: VideoClipData) => {
      e.preventDefault();
      e.stopPropagation();

      // 如果片段未激活，先激活片段
      if (!isActiveClip(clip.id)) {
        handleClipActivate(clip);
        // 激活后继续执行拖拽逻辑，不要返回
      }

      setIsDragging(true);
      setDragType("outPoint");
      setDraggingClipId(clip.id);

      // 记录拖拽开始时的滚动位置
      const container = document.querySelector(
        ".timeline-main-container"
      ) as HTMLElement;
      dragStartScrollTopRef.current = container ? container.scrollTop : 0;

      dragStartRef.current = {
        y: e.clientY,
        originalValue: clip.outPoint,
      };

      // 创建新的事件处理函数，避免闭包问题
      const handleMouseMoveGlobal = (e: MouseEvent) => {
        if (!dragStartRef.current || !clip.id) return;

        const container = document.querySelector(
          ".timeline-main-container"
        ) as HTMLElement;
        if (!container) return;

        // 先计算当前的滚动偏移量
        const currentScrollOffset =
          container.scrollTop - dragStartScrollTopRef.current;

        // 预测即将发生的滚动
        const containerRect = container.getBoundingClientRect();
        const relativeY = e.clientY - containerRect.top;
        const containerHeight = containerRect.height;
        const scrollThreshold = 50;

        let predictedScrollOffset = currentScrollOffset;

        // 如果鼠标在滚动区域，预测下一帧的滚动偏移
        if (relativeY < scrollThreshold && container.scrollTop > 0) {
          predictedScrollOffset += 4; // 预测向上滚动
        } else if (relativeY > containerHeight - scrollThreshold) {
          const maxScrollTop = container.scrollHeight - container.clientHeight;
          if (container.scrollTop < maxScrollTop) {
            predictedScrollOffset -= 4; // 预测向下滚动
          }
        }

        // 处理自动滚动
        handleAutoScroll(e.clientY);

        const deltaY = e.clientY - dragStartRef.current.y;
        const sensitivity = e.shiftKey ? 0.1 : 1;
        const deltaTime =
          (deltaY * sensitivity) /
          (TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel);

        // 使用预测的滚动偏移量
        const scrollTimeOffset =
          predictedScrollOffset /
          (TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel);
        const newValue = Math.max(
          0,
          dragStartRef.current.originalValue + deltaTime + scrollTimeOffset
        );

        // 限制范围
        const constrainedValue = Math.max(
          clip.inPoint + 0.1,
          Math.min(clip.duration, newValue)
        );

        // 保存最终值到 ref
        finalDragValueRef.current = constrainedValue;

        // 更新实时显示值
        setLiveOutPoint((prev) => ({
          ...prev,
          [clip.id]: constrainedValue,
        }));

        // 设置预览时间为出点的全局时间位置
        const previewGlobalTime = clip.startTime + constrainedValue;
        setPreviewTime(previewGlobalTime);
      };

      const handleMouseUpGlobal = () => {
        // 停止自动滚动
        stopAutoScroll();

        // 获取最终的拖拽值
        const finalValue = finalDragValueRef.current ?? clip.outPoint;

        // 更新全局状态
        const updatedClips = videoClips.map((c: VideoClipData) =>
          c.id === clip.id ? { ...c, outPoint: finalValue } : c
        );
        setVideoClips(updatedClips);

        // 清理实时状态
        setLiveOutPoint((prev) => {
          const newState = { ...prev };
          delete newState[clip.id];
          return newState;
        });

        // 清理预览时间
        setPreviewTime(null);

        // 清理 ref
        finalDragValueRef.current = null;

        setIsDragging(false);
        setDragType(null);
        setDraggingClipId(null);
        dragStartRef.current = null;

        document.removeEventListener("mousemove", handleMouseMoveGlobal);
        document.removeEventListener("mouseup", handleMouseUpGlobal);
      };

      document.addEventListener("mousemove", handleMouseMoveGlobal);
      document.addEventListener("mouseup", handleMouseUpGlobal);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      isActiveClip,
      handleClipActivate,
      setLiveOutPoint,
      setIsDragging,
      setDragType,
      setDraggingClipId,
      zoomLevel,
      handleAutoScroll,
      stopAutoScroll,
    ]
  );

  /**
   * 处理播放头拖拽开始
   */
  const handlePlayheadDragStart = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      setIsDragging(true);
      setDragType("playhead");

      // 记录拖拽开始时的滚动位置
      const container = document.querySelector(
        ".timeline-main-container"
      ) as HTMLElement;
      dragStartScrollTopRef.current = container ? container.scrollTop : 0;

      dragStartRef.current = {
        y: e.clientY,
        originalValue: currentTime,
      };

      // 初始化实时显示值
      setLiveCurrentTime(currentTime);

      // 创建新的事件处理函数，避免闭包问题
      const handleMouseMoveGlobal = (e: MouseEvent) => {
        if (!dragStartRef.current) return;

        const container = document.querySelector(
          ".timeline-main-container"
        ) as HTMLElement;
        if (!container) return;

        // 先计算当前的滚动偏移量
        const currentScrollOffset =
          container.scrollTop - dragStartScrollTopRef.current;

        // 预测即将发生的滚动
        const containerRect = container.getBoundingClientRect();
        const relativeY = e.clientY - containerRect.top;
        const containerHeight = containerRect.height;
        const scrollThreshold = 50;

        let predictedScrollOffset = currentScrollOffset;

        // 如果鼠标在滚动区域，预测下一帧的滚动偏移
        if (relativeY < scrollThreshold && container.scrollTop > 0) {
          predictedScrollOffset += 4; // 预测向上滚动
        } else if (relativeY > containerHeight - scrollThreshold) {
          const maxScrollTop = container.scrollHeight - container.clientHeight;
          if (container.scrollTop < maxScrollTop) {
            predictedScrollOffset -= 4; // 预测向下滚动
          }
        }

        // 处理自动滚动
        handleAutoScroll(e.clientY);

        const deltaY = e.clientY - dragStartRef.current.y;
        const sensitivity = e.shiftKey ? 0.1 : 1;
        const deltaTime =
          (deltaY * sensitivity) /
          (TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel);

        // 使用预测的滚动偏移量
        const scrollTimeOffset =
          predictedScrollOffset /
          (TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel);
        const newValue = Math.max(
          0,
          dragStartRef.current.originalValue + deltaTime + scrollTimeOffset
        );

        // 限制范围
        const constrainedValue = Math.max(0, Math.min(duration, newValue));

        // 保存最终值到 ref
        finalDragValueRef.current = constrainedValue;

        // 更新实时显示值
        setLiveCurrentTime(constrainedValue);

        // 实时更新全局时间状态，让监视器同步显示
        seekToTime(constrainedValue);
      };

      const handleMouseUpGlobal = () => {
        // 停止自动滚动
        stopAutoScroll();

        // 获取最终的拖拽值
        const finalValue = finalDragValueRef.current ?? currentTime;

        // 确保最终状态同步（虽然在拖拽过程中已经实时更新了）
        seekToTime(finalValue);

        // 清理实时状态
        setLiveCurrentTime(null);

        // 清理 ref
        finalDragValueRef.current = null;

        setIsDragging(false);
        setDragType(null);
        dragStartRef.current = null;

        document.removeEventListener("mousemove", handleMouseMoveGlobal);
        document.removeEventListener("mouseup", handleMouseUpGlobal);
      };

      document.addEventListener("mousemove", handleMouseMoveGlobal);
      document.addEventListener("mouseup", handleMouseUpGlobal);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      setLiveCurrentTime,
      setIsDragging,
      setDragType,
      duration,
      zoomLevel,
      currentTime,
      handleAutoScroll,
      stopAutoScroll,
    ]
  );

  /**
   * 获取视频预览帧
   * 从服务器异步加载视频的预览帧用于时间轴显示
   * @param clip 视频片段对象
   */
  const fetchVideoFrames = async (clip: VideoClipData) => {
    if (!clip.videoUrl || loadingFrames[clip.id]) {
      return;
    }

    // 从selectedClips中获取episodePath
    const episodePath = (
      selectedClips.find((sc: any) => String(sc.id) === clip.id) as any
    )?.episodePath;

    if (!episodePath) {
      console.warn("No episodePath found for clip:", clip.id);
      return;
    }

    setLoadingFrames((prev) => ({ ...prev, [clip.id]: true }));

    try {
      // 根据视频时长计算所需帧数
      const frameCount = Math.ceil(clip.duration);

      const response = await getShortDramaFrame({
        resourcePath: episodePath,
        frameCount: frameCount,
        snapshotParam: {
          height: TIMELINE_CONSTANTS.FRAME_HEIGHT,
          width: TIMELINE_CONSTANTS.FRAME_WIDTH,
          mode: "exact",
        },
      });

      if (response && response.data && response.data.result) {
        // 提取预览帧URL，优先使用CDN链接
        const frames = response.data.result
          .map(
            (frame: any) =>
              frame.cdnUrl || frame.url || frame.accelerateUrl || ""
          )
          .filter((url: string) => url);

        setVideoFrames((prev) => ({
          ...prev,
          [clip.id]: frames,
        }));
      }
    } catch (error) {
      console.error("Failed to fetch video frames:", error);
    } finally {
      setLoadingFrames((prev) => ({ ...prev, [clip.id]: false }));
    }
  };

  // 当视频片段变化时，异步加载预览帧
  useEffect(() => {
    if (videoClips && videoClips.length > 0) {
      videoClips.forEach((clip: VideoClipData) => {
        // 只为还没有加载帧的片段加载帧
        if (!videoFrames[clip.id] && !loadingFrames[clip.id]) {
          fetchVideoFrames(clip);
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [videoClips, selectedClips]);

  // 清理事件监听器
  useEffect(() => {
    return () => {
      // 清理可能残留的事件监听器
      setIsDragging(false);
      setDragType(null);
      setDraggingClipId(null);
      dragStartRef.current = null;
      finalDragValueRef.current = null;
      // 清理实时状态
      setLiveInPoint({});
      setLiveOutPoint({});
      setLiveCurrentTime(null);
    };
  }, []);

  // 计算时间刻度尺的实际高度 - 使用useMemo优化
  const rulerHeight = useMemo(() => {
    return Math.max(
      duration * TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel,
      1000
    );
  }, [duration, zoomLevel]);

  /**
   * 处理刻度尺点击事件
   */
  const handleRulerClick = (e: React.MouseEvent) => {
    // 如果正在拖拽播放头，不处理点击事件
    if (isDragging && dragType === "playhead") {
      return;
    }

    const rect = e.currentTarget.getBoundingClientRect();
    const clickY = e.clientY - rect.top;
    const time = Math.max(
      0,
      Math.min(
        duration,
        clickY / (TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel)
      )
    );

    seekToTime(time);
  };

  /**
   * 生成视频轨道片段（保持原有蓝色样式，内部集成帧预览）
   */
  const generateVideoTracks = useCallback(() => {
    if (!videoClips || videoClips.length === 0) {
      return null;
    }

    return videoClips.map((clip: VideoClipData) => {
      const startPosition =
        clip.startTime * TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel;
      // 视频片段在时间轴上的高度应该基于 endTime - startTime，而不是 inPoint - outPoint
      const clipHeight =
        (clip.endTime - clip.startTime) *
        TIMELINE_CONSTANTS.PIXELS_PER_SECOND *
        zoomLevel;

      // 帧预览相关数据
      const clipFrames = videoFrames[clip.id] || [];
      const isLoading = loadingFrames[clip.id];
      const frameHeight = TIMELINE_CONSTANTS.PIXELS_PER_SECOND * zoomLevel;
      const frameCount = Math.ceil(clip.duration);

      // 检查是否为激活片段
      const isActive = isActiveClip(clip.id);

      // 获取实时的入点出点值
      const { currentInPoint, currentOutPoint } = getLiveClipPoints(clip);

      // 计算入点和出点的位置（使用实时值）
      const inPointPosition = (currentInPoint / clip.duration) * clipHeight;
      const outPointPosition = (currentOutPoint / clip.duration) * clipHeight;
      const activeRangeHeight = outPointPosition - inPointPosition;

      return (
        <div
          key={`video-clip-${clip.id}`}
          className={`absolute left-0 right-0 border-1 cursor-pointer overflow-hidden transition-all duration-200 z-[50] ${
            isActive ? "border-blue-600" : "border-blue-500"
          }`}
          style={{
            top: `${startPosition}px`,
            height: `${clipHeight}px`,
            minHeight: "20px",
          }}
          onClick={(e) => {
            if (isDragging && dragType === "playhead") {
              e.stopPropagation();
              return;
            }
            e.stopPropagation();
            handleClipActivate(clip);
          }}
        >
          {/* 帧预览区域 - 在左侧显示所有帧*/}
          <div
            className={`absolute left-0 top-0 bottom-0 overflow-hidden z-0 pointer-events-none border-2 rounded-md ${
              isActive ? "border-blue-500" : "border-gray-400"
            }`}
            style={{
              width: `${TIMELINE_CONSTANTS.VIDEO_TRACK_WIDTH}px`,
              backgroundColor: "rgba(59, 130, 246, 0.05)",
            }}
          >
            {/* 加载状态显示 */}
            {isLoading && (
              <div className="absolute inset-0 bg-blue-50 flex items-center justify-center">
                <Spin size="small" />
              </div>
            )}

            {/* 预览帧显示区域 - 显示所有帧 */}
            {!isLoading && frameCount > 0 && (
              <div className="relative w-full h-full">
                {Array.from({ length: frameCount }).map((_, frameIndex) => {
                  const frameUrl = clipFrames[frameIndex] || "";

                  // 计算当前帧是否在实际使用范围内（使用实时值）
                  const isInActiveRange =
                    frameIndex >= currentInPoint &&
                    frameIndex <= currentOutPoint;

                  return (
                    <div
                      key={`frame-${clip.id}-${frameIndex}`}
                      className="absolute left-0 w-full overflow-hidden"
                      style={{
                        top: `${frameIndex * frameHeight}px`,
                        height: `${frameHeight}px`,
                      }}
                    >
                      {frameUrl ? (
                        <div className="relative w-full h-full">
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            src={frameUrl}
                            alt={`Frame ${frameIndex}`}
                            className="w-full h-full object-cover"
                            style={{ objectFit: "cover" }}
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = "none";
                            }}
                          />
                          {/* 为激活范围外的帧添加遮罩 */}
                          {/* {!isInActiveRange && (
                            <div className="absolute inset-0 bg-gray-500 bg-opacity-50"></div>
                          )} */}
                        </div>
                      ) : (
                        <div
                          className={`w-full h-full flex items-center justify-center ${
                            isInActiveRange ? "bg-blue-100" : "bg-gray-200"
                          }`}
                        >
                          <div
                            className={`text-xs ${
                              isInActiveRange
                                ? "text-blue-600"
                                : "text-gray-400"
                            }`}
                          >
                            {frameIndex + 1}s
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* 实际使用的视频片段范围高亮显示 */}
          <div
            className="absolute inset-0 z-5 pointer-events-none"
            style={{
              marginLeft: `${TIMELINE_CONSTANTS.VIDEO_TRACK_WIDTH}px`,
            }}
          >
            {/* 显示实际使用的视频片段范围 */}
            <div
              className={`absolute left-0 right-0 ${
                isActive ? "bg-blue-400/20" : "bg-blue-300/10"
              }`}
              style={{
                top: `${inPointPosition}px`,
                height: `${Math.max(activeRangeHeight, 2)}px`,
              }}
            ></div>

            {/* 片段标签 - 显示在入点位置下面 */}
            <div
              className={`absolute right-2 transition-colors duration-200 pointer-events-none z-10 ${
                isActive ? "text-blue-800" : "text-blue-700"
              }`}
              style={{
                top: `${inPointPosition + 4}px`,
              }}
            >
              <div className="text-xs font-medium bg-white/80 px-1 py-0.5 rounded">
                片段 {clip.indexNumber} {isActive ? "(选中)" : ""}
              </div>
            </div>
          </div>
        </div>
      );
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    videoClips,
    zoomLevel,
    videoFrames,
    loadingFrames,
    isDragging,
    dragType,
    activeClip,
    liveInPoint,
    liveOutPoint,
    draggingClipId,
  ]);

  return (
    <div
      className={`relative overflow-y-auto bg-muted/10 max-h-[calc(100vh-88px)] py-4 timeline-main-container ${
        isDragging && dragType !== "playhead"
          ? "cursor-row-resize select-none"
          : ""
      }`}
    >
      <div className="flex">
        {/* 时间刻度尺 */}
        <Ruler
          duration={duration}
          zoomLevel={zoomLevel}
          pixelsPerSecond={TIMELINE_CONSTANTS.PIXELS_PER_SECOND}
          onRulerClick={handleRulerClick}
        />

        {/* 视频轨道区域 */}
        <div
          className="relative w-full ml-4 pr-2"
          style={{ height: `${rulerHeight}px` }}
        >
          {/* 视频片段轨道 */}
          <div className="relative w-full h-full pt-8">
            {generateVideoTracks()}
          </div>

          {/* 字幕轨道 */}
          {zoomLevel > 0.2 && <SubtitleTrack />}
        </div>
      </div>

      {/* 当前时间指示线 */}
      {currentTime >= 0 && (
        <div
          className={`absolute left-7 right-2 h-0.5 bg-red-500 pointer-events-auto z-[999] hover:cursor-row-resize ${
            isDragging && dragType === "playhead"
              ? "cursor-grabbing"
              : "cursor-grab"
          }`}
          style={{
            top: `${
              (liveCurrentTime !== null ? liveCurrentTime : currentTime) *
                TIMELINE_CONSTANTS.PIXELS_PER_SECOND *
                zoomLevel +
              16
            }px`,
            boxShadow: "0 0 4px rgba(239, 68, 68, 0.5)",
          }}
          onMouseDown={handlePlayheadDragStart}
          onClick={(e) => e.stopPropagation()}
        >
          <div
            className={`z-[999] absolute -top-[3px] rounded-r-full w-6 h-2 -left-5 bg-red-500 flex items-center justify-center transition-transform duration-200 ${
              isDragging && dragType === "playhead"
                ? "cursor-grabbing scale-110"
                : "cursor-grab active:cursor-grabbing hover:cursor-grab hover:scale-110"
            }`}
          />
          {/* 悬停提示 */}
          <div className="absolute -left-16 -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
            播放头:{" "}
            {(liveCurrentTime !== null ? liveCurrentTime : currentTime).toFixed(
              1
            )}
            s
          </div>
        </div>
      )}

      {/* 入点出点指示器 - 与播放头指示器完全相同的长度和样式 */}
      {videoClips &&
        videoClips.map((clip: VideoClipData) => {
          const isActive = isActiveClip(clip.id);

          // 获取实时的入点出点值
          const { currentInPoint, currentOutPoint } = getLiveClipPoints(clip);

          // 计算入点和出点的全局位置（使用实时值）
          const inPointGlobalPosition =
            (clip.startTime + currentInPoint) *
              TIMELINE_CONSTANTS.PIXELS_PER_SECOND *
              zoomLevel +
            16;
          const outPointGlobalPosition =
            (clip.startTime + currentOutPoint) *
              TIMELINE_CONSTANTS.PIXELS_PER_SECOND *
              zoomLevel +
            16;

          return (
            <Fragment key={`indicators-${clip.id}`}>
              {/* 入点指示器 */}
              <div
                className={`absolute h-0.5 z-[999] pointer-events-auto hover:cursor-row-resize ${
                  isActive &&
                  draggingClipId === clip.id &&
                  dragType === "inPoint"
                    ? "bg-blue-700"
                    : isActive
                    ? "bg-blue-600"
                    : "bg-blue-500"
                }`}
                style={{
                  left: "28px", // 刻度尺宽度 + 一些边距
                  right: "8px", // 右边距
                  top: `${inPointGlobalPosition}px`,
                  boxShadow: "0 0 4px rgba(59, 130, 246, 0.5)",
                  pointerEvents: isActive ? "auto" : "none", // 只有激活状态才可交互
                }}
                onMouseDown={(e) => {
                  if (isDragging && dragType === "playhead") {
                    e.stopPropagation();
                    return;
                  }
                  handleInPointDragStart(e, clip);
                }}
                onClick={(e) => e.stopPropagation()}
              >
                <div
                  className={`z-[999] absolute -top-[3px] rounded-r-full w-6 h-2 -left-5 flex items-center justify-center transition-transform duration-200 ${
                    isActive &&
                    draggingClipId === clip.id &&
                    dragType === "inPoint"
                      ? "bg-blue-700 cursor-grabbing scale-110"
                      : isActive
                      ? "bg-blue-600 cursor-grab active:cursor-grabbing hover:cursor-grab hover:scale-110"
                      : "bg-blue-500 cursor-grab active:cursor-grabbing hover:cursor-grab hover:scale-110"
                  }`}
                />
              </div>

              {/* 出点指示器 */}
              <div
                className={`absolute h-0.5 z-[999] pointer-events-auto hover:cursor-row-resize ${
                  isActive &&
                  draggingClipId === clip.id &&
                  dragType === "outPoint"
                    ? "bg-blue-700"
                    : isActive
                    ? "bg-blue-600"
                    : "bg-blue-500"
                }`}
                style={{
                  left: "28px", // 刻度尺宽度 + 一些边距
                  right: "8px", // 右边距
                  top: `${outPointGlobalPosition}px`,
                  boxShadow: "0 0 4px rgba(59, 130, 246, 0.5)",
                  pointerEvents: isActive ? "auto" : "none", // 只有激活状态才可交互
                }}
                onMouseDown={(e) => {
                  if (isDragging && dragType === "playhead") {
                    e.stopPropagation();
                    return;
                  }
                  handleOutPointDragStart(e, clip);
                }}
                onClick={(e) => e.stopPropagation()}
              >
                <div
                  className={`z-[999] absolute -top-[3px] rounded-r-full w-6 h-2 -left-5 flex items-center justify-center transition-transform duration-200 ${
                    isActive &&
                    draggingClipId === clip.id &&
                    dragType === "outPoint"
                      ? "bg-blue-700 cursor-grabbing scale-110"
                      : isActive
                      ? "bg-blue-600 cursor-grab active:cursor-grabbing hover:cursor-grab hover:scale-110"
                      : "bg-blue-500 cursor-grab active:cursor-grabbing hover:cursor-grab hover:scale-110"
                  }`}
                />
              </div>
            </Fragment>
          );
        })}
    </div>
  );
}
